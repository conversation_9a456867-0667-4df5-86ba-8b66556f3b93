import { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { AdminErrorBoundary } from '@/components/ui/error-boundary';
import {
  Users,
  User,
  ShoppingCart,
  Package,
  Settings,
  Home,
  Image,
  BarChart3,
  Mail,
  FileText,
  HelpCircle
} from 'lucide-react';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { AdminProducts } from '@/components/admin/AdminProducts';
import { AdminCategories } from '@/components/admin/AdminCategories';
import { AdminOrders } from '@/components/admin/AdminOrders';
import { AdminUsers } from '@/components/admin/AdminUsers';
import { AdminPortfolio } from '@/components/admin/AdminPortfolio';
import { AdminHomepage } from '@/components/admin/AdminHomepage';
import { AdminArtist } from '@/components/admin/AdminArtist';
import { AdminBlog } from '@/components/admin/AdminBlog';
import { AdminFAQ } from '@/components/admin/AdminFAQ';
import { AdminContact } from '@/components/admin/AdminContact';
import { AdminNewsletter } from '@/components/admin/AdminNewsletter';
import { AdminSettings } from '@/components/admin/AdminSettings';

const DepthAdmin = () => {
  const { user, isAdmin, loading, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');

  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-red-600">Access Denied</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">You don't have admin privileges to access this page.</p>
            <Button onClick={() => window.location.href = '/'} className="w-full">
              Go Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const adminTabs = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'homepage', label: 'Homepage', icon: Home },
    { id: 'artist', label: 'Artist', icon: User },
    { id: 'portfolio', label: 'Portfolio', icon: Image },
    { id: 'products', label: 'Products', icon: Package },
    { id: 'categories', label: 'Categories', icon: FileText },
    { id: 'orders', label: 'Orders', icon: ShoppingCart },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'blog', label: 'Blog', icon: FileText },
    { id: 'faq', label: 'FAQ', icon: HelpCircle },
    { id: 'contact', label: 'Contact', icon: Mail },
    { id: 'newsletter', label: 'Newsletter', icon: Mail },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <AdminErrorBoundary>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-blue-600">Admin Panel</h1>
              <Badge variant="secondary">Depths of Perception</Badge>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Welcome, {user.email}
              </span>
              <Button variant="outline" onClick={signOut}>
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-13 mb-8">
            {adminTabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <TabsTrigger 
                  key={tab.id} 
                  value={tab.id}
                  className="flex items-center gap-2 px-3 py-2"
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <AdminDashboard />
          </TabsContent>

          <TabsContent value="homepage" className="space-y-6">
            <AdminHomepage />
          </TabsContent>

          <TabsContent value="artist" className="space-y-6">
            <AdminArtist />
          </TabsContent>

          <TabsContent value="portfolio" className="space-y-6">
            <AdminPortfolio />
          </TabsContent>

          <TabsContent value="products" className="space-y-6">
            <AdminProducts />
          </TabsContent>

          <TabsContent value="categories" className="space-y-6">
            <AdminCategories />
          </TabsContent>

          <TabsContent value="orders" className="space-y-6">
            <AdminOrders />
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <AdminUsers />
          </TabsContent>

          <TabsContent value="blog" className="space-y-6">
            <AdminBlog />
          </TabsContent>

          <TabsContent value="faq" className="space-y-6">
            <AdminFAQ />
          </TabsContent>

          <TabsContent value="contact" className="space-y-6">
            <AdminContact />
          </TabsContent>

          <TabsContent value="newsletter" className="space-y-6">
            <AdminNewsletter />
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <AdminSettings />
          </TabsContent>
        </Tabs>
      </div>
    </AdminErrorBoundary>
  );
};

export default DepthAdmin;