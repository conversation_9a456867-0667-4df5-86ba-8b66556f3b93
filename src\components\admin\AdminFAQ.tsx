import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { supabase } from '@/integrations/supabase/client';
import { Plus, Edit, Trash2, HelpCircle, ChevronDown, ChevronRight, Tag, Search, ThumbsUp, ThumbsDown } from 'lucide-react';
import { toast } from 'sonner';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category_id: string;
  sort_order: number;
  is_active: boolean;
  helpful_count: number;
  not_helpful_count: number;
  created_at: string;
  updated_at: string;
  category?: {
    name: string;
    color: string;
  };
}

interface FAQCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const AdminFAQ = () => {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [categories, setCategories] = useState<FAQCategory[]>([]);
  const [filteredFaqs, setFilteredFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [showFaqDialog, setShowFaqDialog] = useState(false);
  const [showCategoryDialog, setShowCategoryDialog] = useState(false);
  const [editingFaq, setEditingFaq] = useState<FAQ | null>(null);
  const [editingCategory, setEditingCategory] = useState<FAQCategory | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [expandedFaqs, setExpandedFaqs] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchFaqs();
    fetchCategories();
  }, []);

  useEffect(() => {
    filterFaqs();
  }, [faqs, searchTerm, categoryFilter]);

  const fetchFaqs = async () => {
    try {
      const { data, error } = await supabase
        .from('faqs')
        .select(`
          *,
          category:category_id(name)
        `)
        .order('sort_order');

      if (error) throw error;
      setFaqs(data || []);
    } catch (error) {
      console.error('Error fetching FAQs:', error);
      toast.error('Failed to fetch FAQs');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('faq_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching FAQ categories:', error);
      toast.error('Failed to fetch FAQ categories');
    }
  };

  const filterFaqs = () => {
    let filtered = faqs;

    if (searchTerm) {
      filtered = filtered.filter(faq =>
        faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(faq => faq.category_id === categoryFilter);
    }

    setFilteredFaqs(filtered);
  };

  const handleSaveFaq = async (faqData: Partial<FAQ>) => {
    try {
      if (!faqData.question?.trim()) {
        toast.error('Question is required');
        return;
      }

      if (!faqData.answer?.trim()) {
        toast.error('Answer is required');
        return;
      }

      const faqPayload = {
        question: faqData.question.trim(),
        answer: faqData.answer.trim(),
        category_id: faqData.category_id || null,
        sort_order: faqData.sort_order || 0,
        is_active: faqData.is_active ?? true,
      };

      if (editingFaq) {
        const { error } = await supabase
          .from('faqs')
          .update(faqPayload)
          .eq('id', editingFaq.id);

        if (error) throw error;
        toast.success('FAQ updated successfully!');
      } else {
        const { error } = await supabase
          .from('faqs')
          .insert(faqPayload);

        if (error) throw error;
        toast.success('FAQ created successfully!');
      }

      setShowFaqDialog(false);
      setEditingFaq(null);
      fetchFaqs();
    } catch (error: any) {
      console.error('Error saving FAQ:', error);
      toast.error(`Failed to save FAQ: ${error.message}`);
    }
  };

  const handleDeleteFaq = async (faq: FAQ) => {
    if (!confirm(`Are you sure you want to delete this FAQ?`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('faqs')
        .delete()
        .eq('id', faq.id);

      if (error) throw error;
      toast.success('FAQ deleted successfully!');
      fetchFaqs();
    } catch (error: any) {
      console.error('Error deleting FAQ:', error);
      toast.error(`Failed to delete FAQ: ${error.message}`);
    }
  };

  const handleSaveCategory = async (categoryData: Partial<FAQCategory>) => {
    try {
      if (!categoryData.name?.trim()) {
        toast.error('Category name is required');
        return;
      }

      const slug = categoryData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

      const categoryPayload = {
        name: categoryData.name.trim(),
        slug,
        description: categoryData.description || '',
        sort_order: categoryData.sort_order || 0,
        is_active: categoryData.is_active ?? true,
      };

      if (editingCategory) {
        const { error } = await supabase
          .from('faq_categories')
          .update(categoryPayload)
          .eq('id', editingCategory.id);

        if (error) throw error;
        toast.success('Category updated successfully!');
      } else {
        const { error } = await supabase
          .from('faq_categories')
          .insert(categoryPayload);

        if (error) throw error;
        toast.success('Category created successfully!');
      }

      setShowCategoryDialog(false);
      setEditingCategory(null);
      fetchCategories();
    } catch (error: any) {
      console.error('Error saving category:', error);
      toast.error(`Failed to save category: ${error.message}`);
    }
  };

  const toggleFaqExpansion = (faqId: string) => {
    const newExpanded = new Set(expandedFaqs);
    if (newExpanded.has(faqId)) {
      newExpanded.delete(faqId);
    } else {
      newExpanded.add(faqId);
    }
    setExpandedFaqs(newExpanded);
  };

  if (loading) {
    return <div className="text-center py-8">Loading FAQs...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">FAQ Management</h2>
          <p className="text-muted-foreground">
            Manage frequently asked questions and categories
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={showCategoryDialog} onOpenChange={setShowCategoryDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" onClick={() => setEditingCategory(null)}>
                <Tag className="h-4 w-4 mr-2" />
                Manage Categories
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? 'Edit Category' : 'Add Category'}
                </DialogTitle>
              </DialogHeader>
              <CategoryForm 
                category={editingCategory}
                onSave={handleSaveCategory}
                onCancel={() => setShowCategoryDialog(false)}
              />
            </DialogContent>
          </Dialog>
          
          <Dialog open={showFaqDialog} onOpenChange={setShowFaqDialog}>
            <DialogTrigger asChild>
              <Button onClick={() => setEditingFaq(null)}>
                <Plus className="h-4 w-4 mr-2" />
                New FAQ
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {editingFaq ? 'Edit FAQ' : 'Create New FAQ'}
                </DialogTitle>
              </DialogHeader>
              <FaqForm 
                faq={editingFaq}
                categories={categories}
                onSave={handleSaveFaq}
                onCancel={() => setShowFaqDialog(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex gap-4 items-center">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search FAQs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </Card>

      {/* FAQ List */}
      <div className="space-y-4">
        {filteredFaqs.map((faq) => (
          <Card key={faq.id}>
            <Collapsible
              open={expandedFaqs.has(faq.id)}
              onOpenChange={() => toggleFaqExpansion(faq.id)}
            >
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {expandedFaqs.has(faq.id) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                      <div className="text-left">
                        <CardTitle className="text-base">{faq.question}</CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          {faq.category && (
                            <Badge variant="outline">
                              {faq.category.name}
                            </Badge>
                          )}
                          {!faq.is_active && (
                            <Badge variant="secondary">Inactive</Badge>
                          )}
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <ThumbsUp className="h-3 w-3" />
                            {faq.helpful_count}
                            <ThumbsDown className="h-3 w-3 ml-2" />
                            {faq.not_helpful_count}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingFaq(faq);
                          setShowFaqDialog(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteFaq(faq);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-0">
                  <div className="prose prose-sm max-w-none">
                    <p className="whitespace-pre-wrap">{faq.answer}</p>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        ))}
      </div>

      {filteredFaqs.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <HelpCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No FAQs yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first FAQ to help customers find answers quickly.
            </p>
            <Button onClick={() => setShowFaqDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create First FAQ
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// FaqForm Component
const FaqForm = ({
  faq,
  categories,
  onSave,
  onCancel
}: {
  faq?: FAQ | null;
  categories: FAQCategory[];
  onSave: (data: Partial<FAQ>) => void;
  onCancel: () => void;
}) => {
  const [formData, setFormData] = useState({
    question: faq?.question || '',
    answer: faq?.answer || '',
    category_id: faq?.category_id || '',
    sort_order: faq?.sort_order || 0,
    is_active: faq?.is_active ?? true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="question">Question *</Label>
        <Textarea
          id="question"
          value={formData.question}
          onChange={(e) => setFormData({ ...formData, question: e.target.value })}
          placeholder="Enter the frequently asked question"
          rows={2}
          required
        />
      </div>

      <div>
        <Label htmlFor="answer">Answer *</Label>
        <Textarea
          id="answer"
          value={formData.answer}
          onChange={(e) => setFormData({ ...formData, answer: e.target.value })}
          placeholder="Enter the answer to the question"
          rows={6}
          required
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="category">Category</Label>
          <Select
            value={formData.category_id}
            onValueChange={(value) => setFormData({ ...formData, category_id: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="sort_order">Sort Order</Label>
          <Input
            id="sort_order"
            type="number"
            value={formData.sort_order}
            onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
            placeholder="0"
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="is_active"
          checked={formData.is_active}
          onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
        />
        <Label htmlFor="is_active">Active</Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {faq ? 'Update FAQ' : 'Create FAQ'}
        </Button>
      </div>
    </form>
  );
};

// CategoryForm Component
const CategoryForm = ({
  category,
  onSave,
  onCancel
}: {
  category?: FAQCategory | null;
  onSave: (data: Partial<FAQCategory>) => void;
  onCancel: () => void;
}) => {
  const [formData, setFormData] = useState({
    name: category?.name || '',
    description: category?.description || '',
    sort_order: category?.sort_order || 0,
    is_active: category?.is_active ?? true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Category Name *</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="Enter category name"
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Category description"
          rows={3}
        />
      </div>

      <div>
        <Label htmlFor="sort_order">Sort Order</Label>
        <Input
          id="sort_order"
          type="number"
          value={formData.sort_order}
          onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
          placeholder="0"
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="is_active"
          checked={formData.is_active}
          onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
        />
        <Label htmlFor="is_active">Active</Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {category ? 'Update Category' : 'Create Category'}
        </Button>
      </div>
    </form>
  );
};
