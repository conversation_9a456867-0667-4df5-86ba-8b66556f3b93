import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { Plus, Mail, Users, Send, Download, Search, Filter, Trash2, UserPlus } from 'lucide-react';
import { toast } from 'sonner';

interface NewsletterSubscriber {
  id: string;
  email: string;
  name: string;
  status: 'active' | 'unsubscribed' | 'bounced';
  source: string;
  tags: string[];
  subscribed_at: string;
  unsubscribed_at: string;
  created_at: string;
  updated_at: string;
}

export const AdminNewsletter = () => {
  const [subscribers, setSubscribers] = useState<NewsletterSubscriber[]>([]);
  const [filteredSubscribers, setFilteredSubscribers] = useState<NewsletterSubscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showCampaignDialog, setShowCampaignDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedSubscribers, setSelectedSubscribers] = useState<Set<string>>(new Set());
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    unsubscribed: 0,
    bounced: 0,
    thisMonth: 0
  });

  useEffect(() => {
    fetchSubscribers();
  }, []);

  useEffect(() => {
    filterSubscribers();
    calculateStats();
  }, [subscribers, searchTerm, statusFilter]);

  const fetchSubscribers = async () => {
    try {
      const { data, error } = await supabase
        .from('newsletter_subscribers')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSubscribers(data || []);
    } catch (error) {
      console.error('Error fetching newsletter subscribers:', error);
      toast.error('Failed to fetch newsletter subscribers');
    } finally {
      setLoading(false);
    }
  };

  const filterSubscribers = () => {
    let filtered = subscribers;

    if (searchTerm) {
      filtered = filtered.filter(subscriber =>
        subscriber.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        subscriber.name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(subscriber => subscriber.status === statusFilter);
    }

    setFilteredSubscribers(filtered);
  };

  const calculateStats = () => {
    const total = subscribers.length;
    const active = subscribers.filter(s => s.status === 'active').length;
    const unsubscribed = subscribers.filter(s => s.status === 'unsubscribed').length;
    const bounced = subscribers.filter(s => s.status === 'bounced').length;
    
    const thisMonth = subscribers.filter(s => {
      const subDate = new Date(s.subscribed_at);
      const now = new Date();
      return subDate.getMonth() === now.getMonth() && subDate.getFullYear() === now.getFullYear();
    }).length;

    setStats({ total, active, unsubscribed, bounced, thisMonth });
  };

  const handleAddSubscriber = async (subscriberData: { email: string; name: string; tags: string[] }) => {
    try {
      if (!subscriberData.email?.trim()) {
        toast.error('Email is required');
        return;
      }

      const { error } = await supabase
        .from('newsletter_subscribers')
        .insert({
          email: subscriberData.email.trim().toLowerCase(),
          name: subscriberData.name?.trim() || '',
          status: 'active',
          source: 'admin',
          tags: subscriberData.tags || [],
          subscribed_at: new Date().toISOString()
        });

      if (error) throw error;
      toast.success('Subscriber added successfully!');
      setShowAddDialog(false);
      fetchSubscribers();
    } catch (error: any) {
      console.error('Error adding subscriber:', error);
      if (error.code === '23505') {
        toast.error('This email is already subscribed');
      } else {
        toast.error(`Failed to add subscriber: ${error.message}`);
      }
    }
  };

  const handleStatusUpdate = async (subscriberId: string, newStatus: string) => {
    try {
      const updates: any = {
        status: newStatus,
        updated_at: new Date().toISOString()
      };

      if (newStatus === 'unsubscribed') {
        updates.unsubscribed_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('newsletter_subscribers')
        .update(updates)
        .eq('id', subscriberId);

      if (error) throw error;
      toast.success('Status updated successfully!');
      fetchSubscribers();
    } catch (error: any) {
      console.error('Error updating status:', error);
      toast.error(`Failed to update status: ${error.message}`);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedSubscribers.size === 0) return;
    
    if (!confirm(`Are you sure you want to delete ${selectedSubscribers.size} subscribers? This action cannot be undone.`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('newsletter_subscribers')
        .delete()
        .in('id', Array.from(selectedSubscribers));

      if (error) throw error;
      toast.success(`${selectedSubscribers.size} subscribers deleted successfully!`);
      setSelectedSubscribers(new Set());
      fetchSubscribers();
    } catch (error: any) {
      console.error('Error deleting subscribers:', error);
      toast.error(`Failed to delete subscribers: ${error.message}`);
    }
  };

  const handleExportSubscribers = () => {
    const csvContent = [
      ['Email', 'Name', 'Status', 'Source', 'Tags', 'Subscribed Date'].join(','),
      ...filteredSubscribers.map(subscriber => [
        `"${subscriber.email}"`,
        `"${subscriber.name || ''}"`,
        `"${subscriber.status}"`,
        `"${subscriber.source || ''}"`,
        `"${subscriber.tags?.join(';') || ''}"`,
        `"${new Date(subscriber.subscribed_at).toLocaleDateString()}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    toast.success('Subscribers exported successfully!');
  };

  const handleSelectSubscriber = (subscriberId: string) => {
    const newSelected = new Set(selectedSubscribers);
    if (newSelected.has(subscriberId)) {
      newSelected.delete(subscriberId);
    } else {
      newSelected.add(subscriberId);
    }
    setSelectedSubscribers(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedSubscribers.size === filteredSubscribers.length) {
      setSelectedSubscribers(new Set());
    } else {
      setSelectedSubscribers(new Set(filteredSubscribers.map(s => s.id)));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'unsubscribed': return 'bg-gray-100 text-gray-800';
      case 'bounced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return <div className="text-center py-8">Loading newsletter subscribers...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Newsletter Management</h2>
          <p className="text-muted-foreground">
            Manage newsletter subscribers and campaigns
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportSubscribers}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Dialog open={showCampaignDialog} onOpenChange={setShowCampaignDialog}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Send className="h-4 w-4 mr-2" />
                Send Campaign
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Send Newsletter Campaign</DialogTitle>
              </DialogHeader>
              <CampaignForm 
                subscribers={filteredSubscribers.filter(s => s.status === 'active')}
                onCancel={() => setShowCampaignDialog(false)}
              />
            </DialogContent>
          </Dialog>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Subscriber
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Subscriber</DialogTitle>
              </DialogHeader>
              <SubscriberForm 
                onSave={handleAddSubscriber}
                onCancel={() => setShowAddDialog(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Mail className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unsubscribed</CardTitle>
            <UserPlus className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.unsubscribed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bounced</CardTitle>
            <Filter className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.bounced}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Plus className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.thisMonth}</div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      {selectedSubscribers.size > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">
                {selectedSubscribers.size} subscriber{selectedSubscribers.size !== 1 ? 's' : ''} selected
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedSubscribers(new Set())}
              >
                Clear Selection
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="destructive"
                size="sm"
                onClick={handleBulkDelete}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Filters */}
      <Card className="p-4">
        <div className="flex gap-4 items-center">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search subscribers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
              <SelectItem value="bounced">Bounced</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleSelectAll}>
            {selectedSubscribers.size === filteredSubscribers.length ? 'Deselect All' : 'Select All'}
          </Button>
        </div>
      </Card>

      {/* Subscribers List */}
      <Card>
        <CardHeader>
          <CardTitle>Subscribers ({filteredSubscribers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredSubscribers.map((subscriber) => (
              <div key={subscriber.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <input
                    type="checkbox"
                    checked={selectedSubscribers.has(subscriber.id)}
                    onChange={() => handleSelectSubscriber(subscriber.id)}
                    className="rounded"
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{subscriber.email}</span>
                      {subscriber.name && (
                        <span className="text-sm text-muted-foreground">({subscriber.name})</span>
                      )}
                      <Badge className={getStatusColor(subscriber.status)}>
                        {subscriber.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                      <span>Subscribed: {new Date(subscriber.subscribed_at).toLocaleDateString()}</span>
                      {subscriber.source && <span>Source: {subscriber.source}</span>}
                      {subscriber.tags?.length > 0 && (
                        <span>Tags: {subscriber.tags.join(', ')}</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Select
                    value={subscriber.status}
                    onValueChange={(value) => handleStatusUpdate(subscriber.id, value)}
                  >
                    <SelectTrigger className="w-[120px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
                      <SelectItem value="bounced">Bounced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ))}
          </div>

          {filteredSubscribers.length === 0 && (
            <div className="text-center py-8">
              <Mail className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No subscribers yet</h3>
              <p className="text-muted-foreground mb-4">
                Add subscribers to start building your newsletter audience.
              </p>
              <Button onClick={() => setShowAddDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Subscriber
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// SubscriberForm Component
const SubscriberForm = ({
  onSave,
  onCancel
}: {
  onSave: (data: { email: string; name: string; tags: string[] }) => void;
  onCancel: () => void;
}) => {
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    tags: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const tags = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    onSave({
      email: formData.email,
      name: formData.name,
      tags
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="email">Email Address *</Label>
        <Input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          placeholder="<EMAIL>"
          required
        />
      </div>

      <div>
        <Label htmlFor="name">Name (Optional)</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="Subscriber name"
        />
      </div>

      <div>
        <Label htmlFor="tags">Tags (Optional)</Label>
        <Input
          id="tags"
          value={formData.tags}
          onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
          placeholder="customer, vip, newsletter (comma separated)"
        />
        <p className="text-xs text-muted-foreground mt-1">
          Separate multiple tags with commas
        </p>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Add Subscriber
        </Button>
      </div>
    </form>
  );
};

// CampaignForm Component
const CampaignForm = ({
  subscribers,
  onCancel
}: {
  subscribers: NewsletterSubscriber[];
  onCancel: () => void;
}) => {
  const [formData, setFormData] = useState({
    subject: '',
    content: '',
    previewText: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real implementation, this would send the campaign
    toast.success(`Campaign "${formData.subject}" would be sent to ${subscribers.length} subscribers`);
    onCancel();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-muted p-4 rounded-lg">
        <p className="text-sm">
          <strong>Recipients:</strong> {subscribers.length} active subscribers
        </p>
      </div>

      <div>
        <Label htmlFor="subject">Subject Line *</Label>
        <Input
          id="subject"
          value={formData.subject}
          onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
          placeholder="Your newsletter subject"
          required
        />
      </div>

      <div>
        <Label htmlFor="previewText">Preview Text</Label>
        <Input
          id="previewText"
          value={formData.previewText}
          onChange={(e) => setFormData({ ...formData, previewText: e.target.value })}
          placeholder="Text that appears in email preview"
        />
      </div>

      <div>
        <Label htmlFor="content">Email Content *</Label>
        <Textarea
          id="content"
          value={formData.content}
          onChange={(e) => setFormData({ ...formData, content: e.target.value })}
          placeholder="Write your newsletter content here..."
          rows={10}
          required
        />
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          <Send className="h-4 w-4 mr-2" />
          Send Campaign
        </Button>
      </div>
    </form>
  );
};
