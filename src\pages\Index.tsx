import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Eye, ShoppingCart, Mail, Fish } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import Navbar from "@/components/Navbar";
import FishAnimation from "@/components/FishAnimation";
import ProductModal from "@/components/ProductModal";
import DatabaseTest from "@/components/DatabaseTest";
import AdminTestAuth from "@/components/AdminTestAuth";
import { StorageTest } from "@/components/StorageTest";
import { useToast } from "@/hooks/use-toast";

interface ArtistProfile {
  id: string;
  name: string;
  bio: string;
  full_biography: string;
  portrait_url: string;
  hero_image_url: string;
}

interface HomepageSettings {
  hero_title: string;
  hero_subtitle: string;
  hero_image_url: string;
  about_title: string;
  about_description: string;
  about_image_url: string;
  featured_portfolio_title: string;
  featured_products_title: string;
  contact_cta_title: string;
  contact_cta_description: string;
}

interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  image_url: string;
  category: string;
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  material: string;
  size: string;
  in_stock: boolean;
}

const Index = () => {
  const [artist, setArtist] = useState<ArtistProfile | null>(null);
  const [featuredWork, setFeaturedWork] = useState<PortfolioItem[]>([]);
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      // Fetch artist profile
      const { data: artistData } = await supabase
        .from('artist_profile')
        .select('*')
        .single();
      
      if (artistData) setArtist(artistData);

      // Fetch featured portfolio items
      const { data: portfolioData } = await supabase
        .from('portfolio_items')
        .select('*')
        .eq('featured', true)
        .order('sort_order')
        .limit(4);
      
      if (portfolioData) setFeaturedWork(portfolioData);

      // Fetch featured products
      const { data: productsData } = await supabase
        .from('products')
        .select('*')
        .eq('featured', true)
        .order('sort_order')
        .limit(6);
      
      if (productsData) setFeaturedProducts(productsData);
    };

    fetchData();
  }, []);

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
    setIsModalOpen(true);
  };

  const handleQuickAddToCart = async (product: Product, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!product.in_stock) return;
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    toast({
      title: "Added to Cart",
      description: `${product.name} added to your cart.`,
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${artist?.hero_image_url})` }}
        />
        <div className="absolute inset-0 wave-overlay" />
        <FishAnimation />
        
        <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
          <h1 className="text-6xl md:text-8xl font-bold mb-6 animate-fade-in">
            Depths of Perception
          </h1>
          <p className="text-xl md:text-2xl mb-8 opacity-90 animate-fade-in" style={{ animationDelay: "0.2s" }}>
            Custom Aquarium Decor from the Depths of Imagination
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in" style={{ animationDelay: "0.4s" }}>
            <Link to="/portfolio">
              <Button size="lg" className="bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30">
                <Eye className="mr-2 h-5 w-5" />
                View Portfolio
              </Button>
            </Link>
            <Link to="/shop">
              <Button size="lg" variant="outline" className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border-white/30 text-white hover:text-white">
                <ShoppingCart className="mr-2 h-5 w-5" />
                Shop Decor
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Artist Section - Expanded Showcase */}
      {artist && (
        <section className="py-32 bg-gradient-to-br from-primary/5 to-accent/5 relative overflow-hidden">
          <FishAnimation />
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-6xl mx-auto">
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <div className="space-y-8">
                  <div className="space-y-6">
                    <Badge variant="secondary" className="text-sm font-medium">Master Sculptor</Badge>
                    <h2 className="text-5xl font-bold leading-tight">Meet {artist.name}</h2>
                    <p className="text-xl text-muted-foreground leading-relaxed">
                      {artist.full_biography || artist.bio}
                    </p>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Link to="/portfolio">
                      <Button size="lg" className="ocean-gradient text-white w-full sm:w-auto">
                        Explore Portfolio
                      </Button>
                    </Link>
                    <Link to="/contact">
                      <Button size="lg" variant="outline" className="w-full sm:w-auto">
                        Commission Custom Work
                      </Button>
                    </Link>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-6 pt-8">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">15+</div>
                      <div className="text-sm text-muted-foreground">Years Experience</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">500+</div>
                      <div className="text-sm text-muted-foreground">Custom Pieces</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">200+</div>
                      <div className="text-sm text-muted-foreground">Happy Clients</div>
                    </div>
                  </div>
                </div>
                
                <div className="relative">
                  <div className="relative">
                    <Avatar className="w-80 h-80 mx-auto shadow-2xl">
                      <AvatarImage src={artist.portrait_url} alt={artist.name} className="object-cover" />
                      <AvatarFallback className="text-6xl">{artist.name[0]}</AvatarFallback>
                    </Avatar>
                    <div className="absolute -bottom-6 -right-6 w-24 h-24 ocean-gradient rounded-full flex items-center justify-center">
                      <Fish className="w-12 h-12 text-white" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Featured Work Gallery */}
      <section className="py-20 relative overflow-hidden">
        <FishAnimation />
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Featured Work</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Discover some of our most stunning aquarium decor pieces, each crafted to transform your aquatic environment.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {featuredWork.map((item) => (
              <Card key={item.id} className="group overflow-hidden hover:shadow-lg transition-all duration-300 hover-scale">
                <div className="relative overflow-hidden">
                  <img 
                    src={item.image_url} 
                    alt={item.title}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <Button variant="secondary" size="sm">
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </Button>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-2">{item.title}</h3>
                  <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                  <Badge variant="secondary">{item.category}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center">
            <Link to="/portfolio">
              <Button size="lg">View All Work</Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-card relative overflow-hidden">
        <FishAnimation />
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Featured Products</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Ready-to-ship aquarium decor pieces that will instantly enhance your underwater world.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {featuredProducts.map((product) => (
              <Card 
                key={product.id} 
                className="group overflow-hidden hover:shadow-lg transition-all duration-300 hover-scale cursor-pointer"
                onClick={() => handleProductClick(product)}
              >
                <div className="relative overflow-hidden">
                  <img 
                    src={product.image_url} 
                    alt={product.name}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  {!product.in_stock && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <Badge variant="destructive">Out of Stock</Badge>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <Button variant="secondary" size="sm">
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </Button>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-2">{product.name}</h3>
                  <p className="text-sm text-muted-foreground mb-2 line-clamp-2">{product.description}</p>
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-lg font-bold text-primary">${product.price.toFixed(2)}</span>
                    <Badge variant="outline">{product.size}</Badge>
                  </div>
                  <Button 
                    className="w-full" 
                    disabled={!product.in_stock}
                    onClick={(e) => handleQuickAddToCart(product, e)}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    {product.in_stock ? 'Add to Cart' : 'Out of Stock'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center">
            <Link to="/shop">
              <Button size="lg">Shop All Products</Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="py-20 ocean-gradient text-white relative overflow-hidden">
        <FishAnimation />
        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl font-bold mb-4">Have a Custom Request?</h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's bring your underwater vision to life. Get in touch to discuss custom aquarium decor tailored to your specific needs.
          </p>
          <Link to="/contact">
            <Button size="lg" variant="secondary">
              <Mail className="mr-2 h-5 w-5" />
              Contact the Artist
            </Button>
          </Link>
        </div>
      </section>

      {/* Product Modal */}
      <ProductModal
        product={selectedProduct}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
      <DatabaseTest />
      <AdminTestAuth />
      <StorageTest />
    </div>
  );
};

export default Index;