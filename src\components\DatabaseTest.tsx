import { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

const DatabaseTest = () => {
  const [testResult, setTestResult] = useState<string>('Testing...');
  const [userProfiles, setUserProfiles] = useState<any[]>([]);

  useEffect(() => {
    const testDatabase = async () => {
      try {
        // Test basic connection
        const { data: artistData, error: artistError } = await supabase
          .from('artist_profile')
          .select('*')
          .limit(1);

        if (artistError) {
          setTestResult(`Artist profile error: ${artistError.message}`);
          return;
        }

        // Test user profiles query that was failing
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .select('*')
          .limit(5);

        if (profileError) {
          setTestResult(`User profiles error: ${profileError.message}`);
          return;
        }

        setUserProfiles(profileData || []);
        setTestResult('Database connection successful!');
      } catch (error) {
        setTestResult(`Unexpected error: ${error}`);
      }
    };

    testDatabase();
  }, []);

  return (
    <div className="fixed bottom-4 right-4 bg-white p-4 rounded-lg shadow-lg border max-w-sm z-50">
      <h3 className="font-bold mb-2">Database Test</h3>
      <p className="text-sm mb-2">{testResult}</p>
      {userProfiles.length > 0 && (
        <div>
          <p className="text-xs font-semibold">User Profiles ({userProfiles.length}):</p>
          {userProfiles.map((profile, index) => (
            <div key={index} className="text-xs">
              {profile.email} - {profile.role}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DatabaseTest;
